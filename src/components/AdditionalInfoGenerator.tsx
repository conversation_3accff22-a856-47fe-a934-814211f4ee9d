'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { Lo<PERSON>, <PERSON>rk<PERSON>, Info, CheckCircle2 } from 'lucide-react';

interface AdditionalInfoSuggestion {
  category: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

interface AdditionalInfoResponse {
  suggestions: AdditionalInfoSuggestion[];
  analysis: {
    productType: string;
    detectedFeatures: string[];
    recommendedSections: string[];
  };
}

interface AdditionalInfoGeneratorProps {
  productName: string;
  category: string;
  existingContent: string;
  onContentGenerated: (content: string) => void;
}

// Function to apply text corrections (same as in main component)
const applyTextCorrections = (text: string): string => {
  if (!text) return '';

  let corrected = text;

  // Fix spacing issues
  corrected = corrected.replace(/\s+/g, ' '); // Multiple spaces to single space
  corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remove space before punctuation
  corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2'); // Add space after punctuation

  // Fix capitalization after punctuation
  corrected = corrected.replace(/([.!?])\s+([a-z])/g, (match, punct, letter) => {
    return punct + ' ' + letter.toUpperCase();
  });

  // Common Portuguese spelling corrections
  const corrections: { [key: string]: string } = {
    'qualidadde': 'qualidade',
    'funcionalidadde': 'funcionalidade',
    'resistênte': 'resistente',
    'duravél': 'durável',
    'electrónico': 'eletrónico',
    'electrónicos': 'eletrónicos',
    'electrónica': 'eletrónica',
    'optimizar': 'otimizar',
    'optimizado': 'otimizado'
  };

  Object.entries(corrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  return corrected.trim();
};

const AdditionalInfoGenerator: React.FC<AdditionalInfoGeneratorProps> = ({
  productName,
  category,
  existingContent,
  onContentGenerated
}) => {
  const [suggestions, setSuggestions] = useState<AdditionalInfoSuggestion[]>([]);
  const [selectedSections, setSelectedSections] = useState<string[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [analysis, setAnalysis] = useState<AdditionalInfoResponse['analysis'] | null>(null);
  const [hasAnalyzed, setHasAnalyzed] = useState(false);

  // Auto-analyze when component receives content (if available)
  useEffect(() => {
    if (productName && category && existingContent && !hasAnalyzed) {
      handleAnalyzeContent();
    }
  }, [productName, category, existingContent, hasAnalyzed]);

  const handleAnalyzeContent = async () => {
    if (!productName || !category) {
      toast.error('Nome do produto e categoria são necessários para análise.');
      return;
    }

    setIsLoadingSuggestions(true);
    const toastId = toast.loading('A analisar conteúdo e sugerir informações adicionais...');

    try {
      const response = await fetch('/api/suggest-additional-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'suggest',
          productName,
          category,
          existingContent
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Erro ao analisar conteúdo.');

      setSuggestions(data.suggestions);
      setAnalysis(data.analysis);
      setHasAnalyzed(true);
      
      // Auto-select high priority suggestions
      const highPrioritySections = data.suggestions
        .filter((s: AdditionalInfoSuggestion) => s.priority === 'high')
        .map((s: AdditionalInfoSuggestion) => s.category);
      setSelectedSections(highPrioritySections);

      toast.success(`${data.suggestions.length} sugestões encontradas!`, { id: toastId });
    } catch (error) {
      console.error('Erro ao analisar conteúdo:', error);
      toast.error('Falha ao analisar conteúdo. Tente novamente.', { id: toastId });
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleSectionToggle = (sectionCategory: string, checked: boolean) => {
    setSelectedSections(prev => 
      checked 
        ? [...prev, sectionCategory]
        : prev.filter(s => s !== sectionCategory)
    );
  };

  const handleGenerateContent = async () => {
    if (selectedSections.length === 0) {
      toast.error('Selecione pelo menos uma secção para gerar conteúdo.');
      return;
    }

    setIsGeneratingContent(true);
    const toastId = toast.loading('A gerar informações adicionais...');

    try {
      const response = await fetch('/api/suggest-additional-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          productName,
          category,
          existingContent,
          selectedSections
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Erro ao gerar conteúdo.');

      // Combine all generated content into a single formatted string with proper spacing
      const combinedContent = Object.entries(data.content)
        .map(([section, content]) => {
          const suggestion = suggestions.find(s => s.category === section);
          const title = suggestion?.title || section;

          // Format content with proper line breaks and structure for each item
          const formattedContent = (content as string)
            .split('\n')
            .filter(line => line.trim()) // Remove empty lines
            .map(line => `• ${line.trim()}`) // Add bullet points
            .join('\n');

          // Create well-structured section with proper spacing
          return `**${title}:**\n\n${formattedContent}\n`;
        })
        .join('\n\n'); // Double line break between sections for clear separation

      // Apply automatic error correction to the combined content
      const correctedContent = applyTextCorrections(combinedContent);
      onContentGenerated(correctedContent);
      toast.success('Informações adicionais geradas com sucesso!', { id: toastId });
    } catch (error) {
      console.error('Erro ao gerar conteúdo:', error);
      toast.error('Falha ao gerar informações adicionais.', { id: toastId });
    } finally {
      setIsGeneratingContent(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-gray-600 dark:text-gray-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'Recomendado';
      case 'medium': return 'Útil';
      case 'low': return 'Opcional';
      default: return '';
    }
  };

  if (!productName || !category) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
          <Info className="h-4 w-4" />
          <span className="text-sm">Preencha o nome do produto e categoria para ativar as sugestões de IA</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Analysis Button */}
      {!hasAnalyzed && (
        <div className="space-y-2">
          {!existingContent && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                💡 <strong>Dica:</strong> Para sugestões mais precisas, gere primeiro a descrição principal.
                Mas também pode obter sugestões básicas apenas com o nome e categoria do produto.
              </p>
            </div>
          )}

          <Button
            onClick={handleAnalyzeContent}
            disabled={isLoadingSuggestions}
            variant="outline"
            className="w-full"
          >
            {isLoadingSuggestions ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                A analisar conteúdo...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                {existingContent ? 'Sugerir Informações Adicionais com IA' : 'Analisar Produto e Sugerir Informações'}
              </>
            )}
          </Button>
        </div>
      )}

      {/* Analysis Results */}
      {analysis && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Análise do Produto
          </h4>
          <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
            <p><strong>Tipo:</strong> {analysis.productType}</p>
            {analysis.detectedFeatures.length > 0 && (
              <p><strong>Características detetadas:</strong> {analysis.detectedFeatures.join(', ')}</p>
            )}
          </div>
        </div>
      )}

      {/* Suggestions List */}
      {suggestions.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Selecione as informações adicionais a incluir:
          </h4>
          
          <div className="space-y-2">
            {suggestions.map((suggestion) => (
              <div
                key={suggestion.category}
                className="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50"
              >
                <Checkbox
                  id={suggestion.category}
                  checked={selectedSections.includes(suggestion.category)}
                  onCheckedChange={(checked) => 
                    handleSectionToggle(suggestion.category, checked as boolean)
                  }
                  className="mt-0.5"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <label
                      htmlFor={suggestion.category}
                      className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer"
                    >
                      {suggestion.title}
                    </label>
                    <span className={`text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-800 ${getPriorityColor(suggestion.priority)}`}>
                      {getPriorityLabel(suggestion.priority)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {suggestion.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Generate Button */}
          <Button
            onClick={handleGenerateContent}
            disabled={isGeneratingContent || selectedSections.length === 0}
            className="w-full"
          >
            {isGeneratingContent ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                A gerar conteúdo...
              </>
            ) : (
              <>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Gerar Informações Selecionadas ({selectedSections.length})
              </>
            )}
          </Button>
        </div>
      )}

      {/* Re-analyze Button */}
      {hasAnalyzed && (
        <Button
          onClick={() => {
            setHasAnalyzed(false);
            setSuggestions([]);
            setSelectedSections([]);
            setAnalysis(null);
          }}
          variant="outline"
          size="sm"
          className="w-full"
        >
          <Sparkles className="mr-2 h-3 w-3" />
          Nova Análise
        </Button>
      )}
    </div>
  );
};

export default AdditionalInfoGenerator;
